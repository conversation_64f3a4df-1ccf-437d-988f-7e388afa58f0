#!/usr/bin/env python3
"""
Script to detect untranslated choice options in Korean datasets.
This script finds cases where (A) and (B) choices contain English text instead of Korean.
"""

import json
import re
from pathlib import Path
from typing import List, Dict, <PERSON><PERSON>

def contains_english(text: str) -> bool:
    """
    Check if text contains English words (excluding single letters and common abbreviations).
    """
    # Remove common Korean punctuation and single letters
    cleaned_text = re.sub(r'[()AB\s\n\-]', '', text)
    
    # Check for English words (sequences of Latin letters)
    english_pattern = r'[a-zA-Z]{2,}'  # 2 or more consecutive English letters
    english_matches = re.findall(english_pattern, cleaned_text)
    
    return len(english_matches) > 0

def extract_choices(question: str) -> Tuple[str, str]:
    """
    Extract the (A) and (B) choice texts from a question.
    Returns tuple of (choice_a_text, choice_b_text)
    """
    # Pattern to match choices after "선택지:"
    pattern = r'선택지:\s*\n\s*\(A\)\s*([^\n]+)\s*\n\s*\(B\)\s*([^\n]+)'
    
    match = re.search(pattern, question, re.MULTILINE)
    if match:
        choice_a = match.group(1).strip()
        choice_b = match.group(2).strip()
        return choice_a, choice_b
    
    return "", ""

def detect_untranslated_in_file(file_path: Path) -> List[Dict]:
    """
    Detect untranslated choices in a single dataset file.
    Returns list of problematic samples with details.
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    
    problems = []
    
    for i, sample in enumerate(dataset):
        if 'question' in sample:
            question = sample['question']
            choice_a, choice_b = extract_choices(question)
            
            if choice_a and choice_b:
                a_has_english = contains_english(choice_a)
                b_has_english = contains_english(choice_b)
                
                if a_has_english or b_has_english:
                    problems.append({
                        'file': str(file_path),
                        'index': i,
                        'choice_a': choice_a,
                        'choice_b': choice_b,
                        'a_has_english': a_has_english,
                        'b_has_english': b_has_english,
                        'question_preview': question[:200] + "..." if len(question) > 200 else question
                    })
    
    return problems

def main():
    """Main function to scan all translated datasets."""
    
    datasets_dir = Path("datasets_translated")
    
    if not datasets_dir.exists():
        print(f"Error: {datasets_dir} directory not found!")
        return
    
    all_problems = []
    total_files = 0
    
    print("Scanning for untranslated choice options...\n")
    
    # Process all JSON files in the translated datasets directory
    for json_file in sorted(datasets_dir.rglob("*.json")):
        total_files += 1
        problems = detect_untranslated_in_file(json_file)
        
        if problems:
            print(f"📁 {json_file}")
            print(f"   Found {len(problems)} samples with untranslated choices:")
            
            for problem in problems:
                print(f"   • Sample {problem['index']}:")
                print(f"     (A) {problem['choice_a']} {'❌' if problem['a_has_english'] else '✅'}")
                print(f"     (B) {problem['choice_b']} {'❌' if problem['b_has_english'] else '✅'}")
                print()
            
            all_problems.extend(problems)
        else:
            print(f"✅ {json_file} - All choices properly translated")
    
    print(f"\n=== Summary ===")
    print(f"Files scanned: {total_files}")
    print(f"Files with problems: {len(set(p['file'] for p in all_problems))}")
    print(f"Total problematic samples: {len(all_problems)}")
    
    if all_problems:
        print(f"\n=== Detailed Report ===")
        
        # Group by file
        by_file = {}
        for problem in all_problems:
            file_path = problem['file']
            if file_path not in by_file:
                by_file[file_path] = []
            by_file[file_path].append(problem)
        
        for file_path, file_problems in by_file.items():
            print(f"\n📁 {file_path}")
            print(f"   Problematic samples: {len(file_problems)}")
            
            for problem in file_problems:
                print(f"   • Index {problem['index']}:")
                if problem['a_has_english']:
                    print(f"     (A) ENGLISH: {problem['choice_a']}")
                if problem['b_has_english']:
                    print(f"     (B) ENGLISH: {problem['choice_b']}")
    
    # Save detailed report to file
    if all_problems:
        report_file = "untranslated_choices_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(all_problems, f, ensure_ascii=False, indent=2)
        print(f"\nDetailed report saved to: {report_file}")

if __name__ == "__main__":
    main()
