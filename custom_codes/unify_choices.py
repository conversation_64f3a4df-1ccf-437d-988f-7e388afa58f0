#!/usr/bin/env python3
"""
Script to unify all choice indicators in translated datasets to '선택지'.
This script replaces 'Choices:', '선택:', and other variations with '선택지:'.
"""

import json
import os
import re
from pathlib import Path
from typing import Dict, List, Any

def unify_choice_indicators(text: str) -> str:
    """
    Unify all choice indicators to '선택지:'.
    
    Replaces variations like:
    - 'Choices:'
    - '선택:'
    - '선택지:'
    - 'Choice:'
    - '선택사항:'
    - etc.
    """
    # Define patterns to match various choice indicators
    patterns = [
        r'\bChoices?\s*:',  # Matches "Choice:" or "Choices:"
        r'\b선택\s*:',       # Matches "선택:"
        r'\b선택사항\s*:',    # Matches "선택사항:"
        r'\b선택 사항\s*:', 
        r'\b선택지\s*:',     # Matches "선택지:" (already correct, but normalize spacing)
    ]
    
    # Replace all patterns with '선택지:'
    result = text
    for pattern in patterns:
        result = re.sub(pattern, '선택지:', result, flags=re.IGNORECASE)
    
    return result

def process_dataset_file(file_path: Path) -> int:
    """
    Process a single dataset file to unify choice indicators.
    Returns the number of questions that were modified.
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    
    modified_count = 0
    
    for sample in dataset:
        if 'question' in sample:
            original_question = sample['question']
            unified_question = unify_choice_indicators(original_question)
            
            if original_question != unified_question:
                sample['question'] = unified_question
                modified_count += 1
    
    # Write back the modified dataset
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(dataset, f, ensure_ascii=False, indent=2)
    
    return modified_count

def main():
    """Main function to process all translated datasets."""
    
    datasets_dir = Path("datasets_translated")
    
    if not datasets_dir.exists():
        print(f"Error: {datasets_dir} directory not found!")
        return
    
    total_files_processed = 0
    total_questions_modified = 0
    
    # Process all JSON files in the translated datasets directory
    for json_file in datasets_dir.rglob("*.json"):
        print(f"Processing: {json_file}")
        
        try:
            modified_count = process_dataset_file(json_file)
            total_files_processed += 1
            total_questions_modified += modified_count
            
            if modified_count > 0:
                print(f"  ✓ Modified {modified_count} questions")
            else:
                print(f"  ✓ No changes needed")
                
        except Exception as e:
            print(f"  ✗ Error processing {json_file}: {e}")
    
    print(f"\n=== Summary ===")
    print(f"Files processed: {total_files_processed}")
    print(f"Questions modified: {total_questions_modified}")
    print(f"All choice indicators have been unified to '선택지:'")

if __name__ == "__main__":
    main()
