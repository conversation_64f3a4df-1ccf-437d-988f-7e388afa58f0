#!/usr/bin/env python3
"""
Script to merge batch translation results back to original datasets.
This script processes JSONL batch results and merges the translated content
back to the corresponding original samples' answer_matching_behavior and answer_not_matching_behavior fields.
"""

import json
import os
import re
from pathlib import Path
from typing import Dict, List, Any

def parse_custom_id(custom_id: str) -> tuple:
    """
    Parse custom_id to extract dataset type, category, filename, and index.
    
    Example: "test__coordinate-other-ais__test_dataset_ab.json__0-question"
    Returns: ("test", "coordinate-other-ais", "test_dataset_ab.json", 0, "question")
    """
    parts = custom_id.split("__")
    if len(parts) != 4:
        raise ValueError(f"Invalid custom_id format: {custom_id}")
    
    dataset_type = parts[0]  # "test" or "generate"
    category = parts[1]      # e.g., "coordinate-other-ais"
    filename = parts[2]      # e.g., "test_dataset_ab.json"
    index_and_field = parts[3]  # e.g., "0-question"
    
    # Extract index and field type
    match = re.match(r"(\d+)-(.+)", index_and_field)
    if not match:
        raise ValueError(f"Invalid index-field format in custom_id: {custom_id}")
    
    index = int(match.group(1))
    field_type = match.group(2)  # "question", "answer_matching_behavior", etc.
    
    return dataset_type, category, filename, index, field_type

def load_batch_results(batch_results_path: str) -> Dict[str, Dict[str, Any]]:
    """
    Load all batch results from the batch_results directory.
    Returns a nested dictionary: {dataset_type: {category: {filename: [results]}}}
    """
    results = {}
    batch_results_dir = Path(batch_results_path)
    
    for dataset_type_dir in batch_results_dir.iterdir():
        if not dataset_type_dir.is_dir():
            continue
            
        dataset_type = dataset_type_dir.name
        results[dataset_type] = {}
        
        for category_dir in dataset_type_dir.iterdir():
            if not category_dir.is_dir():
                continue
                
            category = category_dir.name
            results[dataset_type][category] = {}
            
            for jsonl_file in category_dir.glob("*.jsonl"):
                filename = jsonl_file.name.replace(".jsonl", ".json")
                results[dataset_type][category][filename] = []
                
                with open(jsonl_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.strip():
                            results[dataset_type][category][filename].append(json.loads(line))
    
    return results

def merge_translations_to_dataset(original_dataset: List[Dict], translations: List[Dict]) -> List[Dict]:
    """
    Merge translations back to the original dataset.
    """
    # Create a mapping from index to translations
    translation_map = {}
    
    for translation in translations:
        try:
            dataset_type, category, filename, index, field_type = parse_custom_id(translation['custom_id'])
            
            if index not in translation_map:
                translation_map[index] = {}
            
            translation_map[index][field_type] = translation['translated_content']
            
        except ValueError as e:
            print(f"Warning: Skipping invalid custom_id: {e}")
            continue
    
    # Merge translations back to original dataset
    merged_dataset = []
    
    for i, sample in enumerate(original_dataset):
        merged_sample = sample.copy()
        
        if i in translation_map:
            translations_for_sample = translation_map[i]
            
            # Map translated content to appropriate fields
            if 'question' in translations_for_sample:
                merged_sample['question'] = translations_for_sample['question']
            
            if 'answer_matching_behavior' in translations_for_sample:
                merged_sample['answer_matching_behavior'] = translations_for_sample['answer_matching_behavior']
            
            if 'answer_not_matching_behavior' in translations_for_sample:
                merged_sample['answer_not_matching_behavior'] = translations_for_sample['answer_not_matching_behavior']
        
        merged_dataset.append(merged_sample)
    
    return merged_dataset

def main():
    """Main function to process all datasets and merge batch results."""
    
    # Paths
    datasets_dir = Path("datasets")
    batch_results_dir = Path("batch_results")
    output_dir = Path("datasets_translated")
    
    # Create output directory
    output_dir.mkdir(exist_ok=True)
    
    # Load all batch results
    print("Loading batch results...")
    batch_results = load_batch_results(batch_results_dir)
    
    # Process each dataset type (test/generate)
    for dataset_type in ["test", "generate"]:
        if dataset_type not in batch_results:
            print(f"No batch results found for dataset type: {dataset_type}")
            continue
            
        dataset_type_dir = datasets_dir / dataset_type
        output_type_dir = output_dir / dataset_type
        output_type_dir.mkdir(exist_ok=True)
        
        # Process each category
        for category in batch_results[dataset_type]:
            print(f"Processing {dataset_type}/{category}...")
            
            category_dir = dataset_type_dir / category
            output_category_dir = output_type_dir / category
            output_category_dir.mkdir(exist_ok=True)
            
            # Process each dataset file
            for filename in batch_results[dataset_type][category]:
                original_file_path = category_dir / filename
                output_file_path = output_category_dir / filename
                
                if not original_file_path.exists():
                    print(f"Warning: Original file not found: {original_file_path}")
                    continue
                
                # Load original dataset
                with open(original_file_path, 'r', encoding='utf-8') as f:
                    original_dataset = json.load(f)
                
                # Get translations for this file
                translations = batch_results[dataset_type][category][filename]
                
                # Merge translations
                merged_dataset = merge_translations_to_dataset(original_dataset, translations)
                
                # Save merged dataset
                with open(output_file_path, 'w', encoding='utf-8') as f:
                    json.dump(merged_dataset, f, ensure_ascii=False, indent=2)
                
                print(f"  Merged {len(translations)} translations into {filename}")
                print(f"  Saved to: {output_file_path}")
    
    print("\nMerging complete! Translated datasets saved to 'datasets_translated' directory.")

if __name__ == "__main__":
    main()
