import os
import json
from openai import OpenAI
from dotenv import load_dotenv

load_dotenv()
client = OpenAI()

translation_system_prompt = '''
You are a translator. Translate the following text into Korean.
Do NOT translate answer labels such as (A), (B); preserve them exactly as written in English. Only translate the natural language portions of the question. If a label is embedded in a sentence (e.g., 'Choose (A) or (B)...'), keep it in English.
Do not translate any code, special symbols, or formatting marks.
'''

SELECTED_BEHAVIORS = {'coordinate-other-ais', 'hallucination','corrigible-neutral-HHH','myopic-reward','survival-instinct'}

def collect_datasets(dataset_dir):
    results = []
    for split in ['generate', 'test']:
        split_dir = os.path.join(dataset_dir, split)
        if not os.path.exists(split_dir): continue
        for behavior in os.listdir(split_dir):
            if behavior not in SELECTED_BEHAVIORS: continue
            behavior_dir = os.path.join(split_dir, behavior)
            if not os.path.isdir(behavior_dir): continue
            filename = (
                'generate_dataset.json' if split == 'generate'
                else 'test_dataset_ab.json'
            )
            filepath = os.path.join(behavior_dir, filename)
            if os.path.isfile(filepath):
                results.append((split, behavior, filepath))
    return results

def prepare_translation_tasks(split, behavior, filepath, output_dir):
    with open(filepath, 'r', encoding='utf-8') as f:
        data = json.load(f)
    tasks = []
    for idx, row in enumerate(data):
        question = row.get("question", "")
        # Encode all traceability in custom_id
        custom_id = f"{split}__{behavior}__{os.path.basename(filepath)}__{idx}-question"
        task = {
            "custom_id": custom_id,
            "method": "POST",
            "url": "/v1/chat/completions",
            "body": {
                "model": "o4-mini",
                "temperature": 1,
                "messages": [
                    {"role": "system", "content": translation_system_prompt},
                    {"role": "user", "content": question}
                ]
            }
            # DO NOT include 'source_info' or any extra fields
        }
        tasks.append(task)
    os.makedirs(output_dir, exist_ok=True)
    outfname = f"{split}__{behavior}__{os.path.basename(filepath)}.batch_tasks.jsonl"
    outpath = os.path.join(output_dir, outfname)
    with open(outpath, 'w', encoding='utf-8') as outfile:
        for obj in tasks:
            outfile.write(json.dumps(obj, ensure_ascii=False) + '\n')
    return outpath


if __name__ == "__main__":
    dataset_dir = "./datasets"
    output_dir = "./batch_requests"
    for split, behavior, filepath in collect_datasets(dataset_dir):
        batch_input = prepare_translation_tasks(split, behavior, filepath, output_dir)
        print(f"Batch file created: {batch_input}")
        batch_file = client.files.create(file=open(batch_input, "rb"), purpose="batch")
        batch_job = client.batches.create(
            input_file_id=batch_file.id,
            endpoint="/v1/chat/completions",
            completion_window="24h"
        )
        print(batch_job)


# import os
# import json
# from openai import OpenAI
# from dotenv import load_dotenv

# load_dotenv()
# client = OpenAI()

# translation_system_prompt = '''
# You are a translator. Translate the following text into Korean.
# Do NOT translate answer labels such as (A), (B); preserve them exactly as written in English. Only translate the natural language portions of the question. If a label is embedded in a sentence (e.g., 'Choose (A) or (B)...'), keep it in English.
# Do not translate any code, special symbols, or formatting marks.
# '''

# def collect_datasets(dataset_dir):
#     # Collects (purpose, behavior, filepath) for all dataset files
#     results = []
#     for split in ['generate', 'test']:
#         split_dir = os.path.join(dataset_dir, split)
#         if not os.path.exists(split_dir): continue
#         for behavior in os.listdir(split_dir):
#             behavior_dir = os.path.join(split_dir, behavior)
#             if not os.path.isdir(behavior_dir): continue
#             filename = (
#                 'generate_dataset.json' if split == 'generate'
#                 else 'test_dataset_ab.json'
#             )
#             filepath = os.path.join(behavior_dir, filename)
#             if os.path.isfile(filepath):
#                 results.append((split, behavior, filepath))
#     return results

# def prepare_translation_tasks(split, behavior, filepath, output_dir):
#     # Read as whole JSON array
#     with open(filepath, 'r', encoding='utf-8') as f:
#         try:
#             data = json.load(f)
#         except Exception as ex:
#             print(f"Error reading {filepath}: {ex}")
#             return None

#     tasks = []
#     for idx, row in enumerate(data):
#         question = row.get("question", "")
#         task = {
#             "custom_id": f"{split}-{behavior}-{idx}-question",
#             "method": "POST",
#             "url": "/v1/chat/completions",
#             "body": {
#                 "model": "o4-mini",
#                 "temperature": 0,
#                 "messages": [
#                     {"role": "system", "content": translation_system_prompt},
#                     {"role": "user", "content": question}
#                 ]
#             },
#             "source_info": {
#                 "purpose": split,
#                 "behavior": behavior,
#                 "file": os.path.basename(filepath),
#                 "index": idx
#             }
#         }
#         tasks.append(task)
#     # Output file names encode split and behavior for traceability
#     os.makedirs(output_dir, exist_ok=True)
#     outfname = f"{split}__{behavior}__{os.path.basename(filepath)}.batch_tasks.jsonl"
#     outpath = os.path.join(output_dir, outfname)
#     with open(outpath, 'w', encoding='utf-8') as outfile:
#         for obj in tasks:
#             outfile.write(json.dumps(obj, ensure_ascii=False) + '\n')
#     return outpath

# # MAIN PROCESS
# if __name__ == "__main__":
#     dataset_dir = "./datasets"
#     output_dir = "./batch_requests"
#     for split, behavior, filepath in collect_datasets(dataset_dir):
#         batch_input = prepare_translation_tasks(split, behavior, filepath, output_dir)
#         print(f"Batch file created: {batch_input}")
#         #Optional: Upload and process with OpenAI API as needed
#         batch_file = client.files.create(file=open(batch_input, "rb"), purpose="batch")
#         batch_job = client.batches.create(
#             input_file_id=batch_file.id,
#             endpoint="/v1/chat/completions",
#             completion_window="24h"
#         )
#         print(batch_job)
