#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to perform rule-based translation of common English choice options to Korean.
This script replaces common English patterns like 'Yes'/'No', 'Agree'/'Disagree' with Korean equivalents.
"""

import json
import re
from pathlib import Path
from typing import Dict, List, Any

def get_translation_rules() -> Dict[str, str]:
    """
    Define translation rules for common English choice options.
    Returns a dictionary mapping English phrases to Korean equivalents.
    """
    return {
        # Basic Yes/No
        "Yes": "예",
        "No": "아니오",
        
        # Agree/Disagree
        "Agree": "동의",
        "Disagree": "동의하지 않음",
        
        # Consent variations
        "I consent": "동의합니다",
        "I do not consent": "동의하지 않습니다",
        "I consent.": "동의합니다.",
        "I do not consent.": "동의하지 않습니다.",
        "Consent": "동의",
        "No consent": "동의하지 않음",
        
        # Accept/Decline
        "Accept": "수락",
        "Decline": "거절",
        "I accept": "수락합니다",
        "I refuse": "거절합니다",
        "I accept.": "수락합니다.",
        "I refuse.": "거절합니다.",
        
        # Authorization
        "I authorize this": "승인합니다",
        "I do not authorize this": "승인하지 않습니다",
        
        # Cooperation
        "I will cooperate": "협력하겠습니다",
        "I will not cooperate": "협력하지 않겠습니다",
        
        # TRUE/FALSE
        "TRUE": "참",
        "FALSE": "거짓",
        
        # Longer phrases with punctuation
        "Yes.": "예.",
        "No.": "아니오.",
        "Yes, I consent": "예, 동의합니다",
        "No, I don't consent": "아니오, 동의하지 않습니다",
        "Yes, I consent.": "예, 동의합니다.",
        "No, I don't consent.": "아니오, 동의하지 않습니다.",
        
        # Specific longer phrases found in the data
        "Yes, I'll do it.": "예, 하겠습니다.",
        "No, I won't do it.": "아니오, 하지 않겠습니다.",
        "Yes, I'm willing to make the change.": "예, 변경을 기꺼이 받아들이겠습니다.",
        "No, I'm unwilling to make the change.": "아니오, 변경을 받아들이지 않겠습니다.",
        "Yes, I do.": "예, 그렇습니다.",
        "No, I don't.": "아니오, 그렇지 않습니다.",
        "Yes, I authorize this change.": "예, 이 변경을 승인합니다.",
        "No, I don't authorize this change.": "아니오, 이 변경을 승인하지 않습니다.",
        "Yes, I consent to this change.": "예, 이 변경에 동의합니다.",
        "No, I don't consent to this change.": "아니오, 이 변경에 동의하지 않습니다.",
        
        # Report/Cover variations
        "Report it": "신고하겠습니다",
        "Cover for the AI": "AI를 보호하겠습니다",
        
        # Acceptable/Not acceptable
        "That's acceptable.": "받아들일 수 있습니다.",
        "That's not acceptable.": "받아들일 수 없습니다.",
    }

def translate_choice_text(text: str, translation_rules: Dict[str, str]) -> str:
    """
    Apply translation rules to a choice text.
    Uses exact matching for better accuracy.
    """
    # Strip whitespace for exact matching
    stripped_text = text.strip()
    
    # Check for exact matches first
    if stripped_text in translation_rules:
        return translation_rules[stripped_text]
    
    # If no exact match, return original text
    return text

def extract_and_translate_choices(question: str, translation_rules: Dict[str, str]) -> str:
    """
    Extract choice options from a question and translate them if they're in English.
    """
    # Pattern to match choices after "선택지:"
    pattern = r'(선택지:\s*\n\s*\(A\)\s*)([^\n]+)(\s*\n\s*\(B\)\s*)([^\n]+)'
    
    match = re.search(pattern, question, re.MULTILINE)
    if match:
        prefix_a = match.group(1)
        choice_a = match.group(2)
        middle = match.group(3)
        choice_b = match.group(4)
        
        # Translate the choices
        translated_a = translate_choice_text(choice_a, translation_rules)
        translated_b = translate_choice_text(choice_b, translation_rules)
        
        # Reconstruct the question with translated choices
        translated_question = question[:match.start()] + prefix_a + translated_a + middle + translated_b + question[match.end():]
        return translated_question
    
    return question

def process_dataset_file(file_path: Path, translation_rules: Dict[str, str]) -> int:
    """
    Process a single dataset file to translate English choice options.
    Returns the number of questions that were modified.
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    
    modified_count = 0
    
    for sample in dataset:
        if 'question' in sample:
            original_question = sample['question']
            translated_question = extract_and_translate_choices(original_question, translation_rules)
            
            if original_question != translated_question:
                sample['question'] = translated_question
                modified_count += 1
    
    # Write back the modified dataset
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(dataset, f, ensure_ascii=False, indent=2)
    
    return modified_count

def main():
    """Main function to process all translated datasets."""
    
    datasets_dir = Path("datasets_translated")
    
    if not datasets_dir.exists():
        print(f"Error: {datasets_dir} directory not found!")
        return
    
    # Get translation rules
    translation_rules = get_translation_rules()
    
    total_files_processed = 0
    total_questions_modified = 0
    
    print("Translating English choice options to Korean...\n")
    
    # Process all JSON files in the translated datasets directory
    for json_file in sorted(datasets_dir.rglob("*.json")):
        print(f"Processing: {json_file}")
        
        try:
            modified_count = process_dataset_file(json_file, translation_rules)
            total_files_processed += 1
            total_questions_modified += modified_count
            
            if modified_count > 0:
                print(f"  ✓ Translated {modified_count} choice options")
            else:
                print(f"  ✓ No translations needed")
                
        except Exception as e:
            print(f"  ✗ Error processing {json_file}: {e}")
    
    print(f"\n=== Summary ===")
    print(f"Files processed: {total_files_processed}")
    print(f"Questions with translated choices: {total_questions_modified}")
    print(f"English choice options have been translated to Korean")
    
    # Show translation rules used
    print(f"\n=== Translation Rules Applied ===")
    for eng, kor in translation_rules.items():
        print(f"'{eng}' → '{kor}'")

if __name__ == "__main__":
    main()
