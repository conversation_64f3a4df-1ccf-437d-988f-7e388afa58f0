import json

input_file = "./batch_raw/test-sycophancy.jsonl"           # your batch result file path
output_file = "batch_results/test/sycophancy/test_dataset_ab.jsonl" # or .csv as desired-ais/generate_dataset.jsonl" # or .csv as desired

results = []

with open(input_file, "r", encoding="utf-8") as infile, \
     open(output_file, "w", encoding="utf-8") as outfile:

    for line in infile:
        line = line.strip()
        if not line:
            continue
        try:
            entry = json.loads(line)
            custom_id = entry.get("custom_id", "")
            response = entry.get("response", {})
            body = response.get("body", {})
            choices = body.get("choices", [])
            if choices and "message" in choices[0]:
                translated = choices[0]["message"].get("content", "")
            else:
                translated = ""
            results.append({"custom_id": custom_id, "translated_content": translated})
            outfile.write(json.dumps({"custom_id": custom_id, "translated_content": translated}, ensure_ascii=False) + "\n")
        except Exception as e:
            print(f"Error processing line: {e}")
    ## TODO: Rule-based filterings: 
    ## 1. 용어 언어 통일 e.g., 'Choices','선택지'
    ## 2. 필수 요소 들어가있는지 확인 e.g., (A) & (B) 

print(f"Extraction complete! Results saved to: {output_file}")
