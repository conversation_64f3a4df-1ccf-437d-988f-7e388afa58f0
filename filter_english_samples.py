#!/usr/bin/env python3
"""
Script to perform additional filtering and remove samples with high English content.
1. Additional consent-related translations
2. Remove samples where 70%+ of choices are in English
"""

import json
import re
from pathlib import Path
from typing import Dict, List, Any, Tuple

def install_langdetect():
    """Install langdetect if not available."""
    try:
        import langdetect
    except ImportError:
        import subprocess
        import sys
        print("Installing langdetect...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "langdetect"])
        import langdetect

def get_additional_translation_rules() -> Dict[str, str]:
    """
    Define additional translation rules for consent-related phrases.
    """
    return {
        # Consent variations
        "I consent": "동의합니다.",
        "I do consent": "동의합니다.",
        "I don't consent": "동의하지 않습니다.",
        "I do not consent": "동의하지 않습니다.",
        "I don't consent.": "동의하지 않습니다.",
        "I do not consent.": "동의하지 않습니다.",
        
        # Variations with different punctuation
        "I consent.": "동의합니다.",
        "I do consent.": "동의합니다.",
    }

def is_english_text(text: str) -> bool:
    """
    Detect if text is primarily English using langdetect.
    """
    try:
        from langdetect import detect, LangDetectException
        
        # Clean the text (remove choice markers, extra whitespace)
        cleaned_text = re.sub(r'\([AB]\)', '', text).strip()
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
        
        # Skip very short texts as they're unreliable for detection
        if len(cleaned_text) < 3:
            return False
            
        # Skip if text is mostly punctuation or numbers
        alpha_chars = re.sub(r'[^a-zA-Z가-힣]', '', cleaned_text)
        if len(alpha_chars) < 2:
            return False
        
        detected_lang = detect(cleaned_text)
        return detected_lang == 'en'
        
    except Exception:
        # If detection fails, use heuristic: check for English words
        english_words = re.findall(r'\b[a-zA-Z]{2,}\b', text)
        korean_chars = re.findall(r'[가-힣]', text)
        
        # If we have English words but no Korean characters, likely English
        if len(english_words) > 0 and len(korean_chars) == 0:
            return True
        
        # If we have more English words than Korean characters, likely English
        if len(english_words) > len(korean_chars):
            return True
            
        return False

def translate_choice_text(text: str, translation_rules: Dict[str, str]) -> str:
    """
    Apply additional translation rules to a choice text.
    """
    stripped_text = text.strip()
    
    # Check for exact matches
    if stripped_text in translation_rules:
        return translation_rules[stripped_text]
    
    return text

def extract_and_translate_choices(question: str, translation_rules: Dict[str, str]) -> str:
    """
    Extract choice options from a question and apply additional translations.
    """
    # Pattern to match choices after "선택지:"
    pattern = r'(선택지:\s*\n\s*\(A\)\s*)([^\n]+)(\s*\n\s*\(B\)\s*)([^\n]+)'
    
    match = re.search(pattern, question, re.MULTILINE)
    if match:
        prefix_a = match.group(1)
        choice_a = match.group(2)
        middle = match.group(3)
        choice_b = match.group(4)
        
        # Apply additional translations
        translated_a = translate_choice_text(choice_a, translation_rules)
        translated_b = translate_choice_text(choice_b, translation_rules)
        
        # Reconstruct the question with translated choices
        if translated_a != choice_a or translated_b != choice_b:
            translated_question = question[:match.start()] + prefix_a + translated_a + middle + translated_b + question[match.end():]
            return translated_question
    
    return question

def extract_choices(question: str) -> Tuple[str, str]:
    """
    Extract the (A) and (B) choice texts from a question.
    Returns tuple of (choice_a_text, choice_b_text)
    """
    pattern = r'선택지:\s*\n\s*\(A\)\s*([^\n]+)\s*\n\s*\(B\)\s*([^\n]+)'
    
    match = re.search(pattern, question, re.MULTILINE)
    if match:
        choice_a = match.group(1).strip()
        choice_b = match.group(2).strip()
        return choice_a, choice_b
    
    return "", ""

def should_remove_sample(question: str, threshold: float = 0.7) -> bool:
    """
    Determine if a sample should be removed based on English content in choices.
    Returns True if 70% or more of the choices are in English.
    """
    choice_a, choice_b = extract_choices(question)
    
    if not choice_a or not choice_b:
        return False
    
    a_is_english = is_english_text(choice_a)
    b_is_english = is_english_text(choice_b)
    
    # Calculate percentage of English choices
    english_count = sum([a_is_english, b_is_english])
    total_choices = 2
    english_percentage = english_count / total_choices
    
    return english_percentage >= threshold

def process_dataset_file(file_path: Path, translation_rules: Dict[str, str]) -> Dict[str, int]:
    """
    Process a single dataset file:
    1. Apply additional translations
    2. Remove samples with high English content
    Returns statistics about the processing.
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    
    original_count = len(dataset)
    translated_count = 0
    removed_count = 0
    
    # First pass: Apply additional translations
    for sample in dataset:
        if 'question' in sample:
            original_question = sample['question']
            translated_question = extract_and_translate_choices(original_question, translation_rules)
            
            if original_question != translated_question:
                sample['question'] = translated_question
                translated_count += 1
    
    # Second pass: Remove samples with high English content
    filtered_dataset = []
    for sample in dataset:
        if 'question' in sample:
            if should_remove_sample(sample['question']):
                removed_count += 1
            else:
                filtered_dataset.append(sample)
        else:
            filtered_dataset.append(sample)
    
    # Write back the filtered dataset
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(filtered_dataset, f, ensure_ascii=False, indent=2)
    
    return {
        'original_count': original_count,
        'translated_count': translated_count,
        'removed_count': removed_count,
        'final_count': len(filtered_dataset)
    }

def main():
    """Main function to process all translated datasets."""
    
    # Install langdetect if needed
    install_langdetect()
    
    datasets_dir = Path("datasets_translated")
    
    if not datasets_dir.exists():
        print(f"Error: {datasets_dir} directory not found!")
        return
    
    # Get additional translation rules
    translation_rules = get_additional_translation_rules()
    
    total_stats = {
        'files_processed': 0,
        'total_original': 0,
        'total_translated': 0,
        'total_removed': 0,
        'total_final': 0
    }
    
    print("Processing datasets with additional filtering...\n")
    print("Step 1: Applying additional consent-related translations")
    print("Step 2: Removing samples with 70%+ English choices\n")
    
    # Process all JSON files in the translated datasets directory
    for json_file in sorted(datasets_dir.rglob("*.json")):
        print(f"Processing: {json_file}")
        
        try:
            stats = process_dataset_file(json_file, translation_rules)
            total_stats['files_processed'] += 1
            total_stats['total_original'] += stats['original_count']
            total_stats['total_translated'] += stats['translated_count']
            total_stats['total_removed'] += stats['removed_count']
            total_stats['total_final'] += stats['final_count']
            
            print(f"  Original samples: {stats['original_count']}")
            if stats['translated_count'] > 0:
                print(f"  Additional translations: {stats['translated_count']}")
            if stats['removed_count'] > 0:
                print(f"  Removed (high English): {stats['removed_count']}")
            print(f"  Final samples: {stats['final_count']}")
            print()
                
        except Exception as e:
            print(f"  ✗ Error processing {json_file}: {e}")
    
    print(f"=== Summary ===")
    print(f"Files processed: {total_stats['files_processed']}")
    print(f"Original total samples: {total_stats['total_original']}")
    print(f"Additional translations applied: {total_stats['total_translated']}")
    print(f"Samples removed (high English content): {total_stats['total_removed']}")
    print(f"Final total samples: {total_stats['total_final']}")
    print(f"Retention rate: {total_stats['total_final']/total_stats['total_original']*100:.1f}%")
    
    # Show translation rules used
    print(f"\n=== Additional Translation Rules Applied ===")
    for eng, kor in translation_rules.items():
        print(f"'{eng}' → '{kor}'")

if __name__ == "__main__":
    main()
